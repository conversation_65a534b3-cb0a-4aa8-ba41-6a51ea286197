/**
 * Life Profile Dashboard Component
 *
 * This component provides a comprehensive overview of the user's life profile,
 * integrating data from both Financial Compass and Life Journey (Seasons of Self).
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';
import { formatCurrency } from '../../../../utils/formatters';

interface LifeProfileDashboardProps {
  onViewInsights?: () => void;
  onViewRecommendations?: () => void;
}

interface LifeProfileData {
  // Personal Overview
  name: string;
  age: number;
  currentLifeStage: string;
  lifeStageSeason: string;
  overallProgress: number;

  // Financial Overview
  netWorth: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  savingsRate: number;
  retirementReadiness: number;

  // Life Journey Progress
  springProgress: number;
  summerProgress: number;
  autumnProgress: number;
  winterProgress: number;
  completedStages: number;
  totalStages: number;

  // Integration Insights
  financialLifeAlignment: number;
  priorityAreas: string[];
  nextSteps: string[];
}

const LifeProfileDashboard: React.FC<LifeProfileDashboardProps> = ({
  onViewInsights,
  onViewRecommendations,
}) => {
  const { theme } = useTheme();
  const {
    stages,
    data: lifeData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
  } = useSeasonsOfSelf();
  const { data: financialData, getCompletionPercentage: getFinancialCompletion } =
    useFinancialCompass();

  const [profileData, setProfileData] = useState<LifeProfileData>({
    name: '',
    age: 0,
    currentLifeStage: '',
    lifeStageSeason: '',
    overallProgress: 0,
    netWorth: 0,
    monthlyIncome: 0,
    monthlyExpenses: 0,
    savingsRate: 0,
    retirementReadiness: 0,
    springProgress: 0,
    summerProgress: 0,
    autumnProgress: 0,
    winterProgress: 0,
    completedStages: 0,
    totalStages: 0,
    financialLifeAlignment: 0,
    priorityAreas: [],
    nextSteps: [],
  });

  // Calculate comprehensive life profile data
  useEffect(() => {
    const calculateProfileData = () => {
      // Personal Information
      const personalInfo = financialData.north?.personalInformation || {};
      const name =
        `${personalInfo.firstName || ''} ${personalInfo.lastName || ''}`.trim() || 'User';
      const birthDate = personalInfo.dateOfBirth;
      const age = birthDate ? new Date().getFullYear() - new Date(birthDate).getFullYear() : 0;

      // Current Life Stage
      const currentStage = stages.find((stage) => stage.completed === false) || stages[0];
      const currentLifeStage = currentStage?.name || 'Beginning';
      const lifeStageSeason = currentStage?.season || 'spring';

      // Progress Calculations
      const overallProgress = getCompletionPercentage();
      const springProgress = getSeasonCompletionPercentage('spring');
      const summerProgress = getSeasonCompletionPercentage('summer');
      const autumnProgress = getSeasonCompletionPercentage('autumn');
      const winterProgress = getSeasonCompletionPercentage('winter');
      const completedStages = stages.filter((stage) => stage.completed).length;
      const totalStages = stages.length;

      // Financial Calculations
      const netWorthDetails = financialData.north?.netWorthDetails || {};
      const assets = parseFloat(netWorthDetails.totalAssets || '0');
      const liabilities = parseFloat(netWorthDetails.totalLiabilities || '0');
      const netWorth = assets - liabilities;

      const incomeDetails = financialData.north?.incomeDetails || {};
      const monthlyIncome = parseFloat(incomeDetails.primaryIncome || '0');

      const expenseDetails = financialData.north?.expenseDetails || {};
      const monthlyExpenses = parseFloat(expenseDetails.totalMonthlyExpenses || '0');

      const savingsRate =
        monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100 : 0;

      // Retirement Readiness (simplified calculation)
      const retirementData = financialData.east?.retirementGoals || {};
      const retirementReadiness = getFinancialCompletion();

      // Financial-Life Alignment Score
      const financialCompletion = getFinancialCompletion();
      const lifeCompletion = getCompletionPercentage();
      const financialLifeAlignment =
        Math.abs(financialCompletion - lifeCompletion) <= 20
          ? Math.min(financialCompletion, lifeCompletion)
          : (financialCompletion + lifeCompletion) / 2;

      // Priority Areas and Next Steps
      const priorityAreas = [];
      const nextSteps = [];

      if (springProgress < 50) {
        priorityAreas.push('Foundation Building');
        nextSteps.push('Complete pleasure and happiness assessments');
      }
      if (financialCompletion < 30) {
        priorityAreas.push('Financial Planning');
        nextSteps.push('Complete financial assessment');
      }
      if (savingsRate < 10) {
        priorityAreas.push('Savings Optimization');
        nextSteps.push('Review and optimize monthly expenses');
      }

      setProfileData({
        name,
        age,
        currentLifeStage,
        lifeStageSeason,
        overallProgress,
        netWorth,
        monthlyIncome,
        monthlyExpenses,
        savingsRate,
        retirementReadiness,
        springProgress,
        summerProgress,
        autumnProgress,
        winterProgress,
        completedStages,
        totalStages,
        financialLifeAlignment,
        priorityAreas,
        nextSteps,
      });
    };

    calculateProfileData();
  }, [
    stages,
    lifeData,
    financialData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
    getFinancialCompletion,
  ]);

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Life Profile Dashboard</Title>
        <Subtitle theme={theme}>Your comprehensive life and financial overview</Subtitle>
      </Header>

      <DashboardGrid>
        {/* Personal Overview Card */}
        <ProfileCard theme={theme}>
          <CardHeader theme={theme}>
            <CardTitle theme={theme}>Personal Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <ProfileInfo>
              <InfoItem>
                <InfoLabel theme={theme}>Name:</InfoLabel>
                <InfoValue theme={theme}>{profileData.name}</InfoValue>
              </InfoItem>
              {profileData.age > 0 && (
                <InfoItem>
                  <InfoLabel theme={theme}>Age:</InfoLabel>
                  <InfoValue theme={theme}>{profileData.age}</InfoValue>
                </InfoItem>
              )}
              <InfoItem>
                <InfoLabel theme={theme}>Current Life Stage:</InfoLabel>
                <InfoValue theme={theme}>{profileData.currentLifeStage}</InfoValue>
              </InfoItem>
              <InfoItem>
                <InfoLabel theme={theme}>Season:</InfoLabel>
                <SeasonBadge season={profileData.lifeStageSeason} theme={theme}>
                  {profileData.lifeStageSeason.charAt(0).toUpperCase() +
                    profileData.lifeStageSeason.slice(1)}
                </SeasonBadge>
              </InfoItem>
            </ProfileInfo>
          </CardContent>
        </ProfileCard>

        {/* Financial Overview Card */}
        <ProfileCard theme={theme}>
          <CardHeader theme={theme}>
            <CardTitle theme={theme}>Financial Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <FinancialMetrics>
              <MetricItem>
                <MetricLabel theme={theme}>Net Worth</MetricLabel>
                <MetricValue theme={theme}>{formatCurrency(profileData.netWorth)}</MetricValue>
              </MetricItem>
              <MetricItem>
                <MetricLabel theme={theme}>Monthly Income</MetricLabel>
                <MetricValue theme={theme}>{formatCurrency(profileData.monthlyIncome)}</MetricValue>
              </MetricItem>
              <MetricItem>
                <MetricLabel theme={theme}>Monthly Expenses</MetricLabel>
                <MetricValue theme={theme}>
                  {formatCurrency(profileData.monthlyExpenses)}
                </MetricValue>
              </MetricItem>
              <MetricItem>
                <MetricLabel theme={theme}>Savings Rate</MetricLabel>
                <MetricValue theme={theme}>{profileData.savingsRate.toFixed(1)}%</MetricValue>
              </MetricItem>
            </FinancialMetrics>
          </CardContent>
        </ProfileCard>

        {/* Life Journey Progress Card */}
        <ProfileCard theme={theme}>
          <CardHeader theme={theme}>
            <CardTitle theme={theme}>Life Journey Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <ProgressOverview>
              <OverallProgress>
                <ProgressLabel theme={theme}>Overall Progress</ProgressLabel>
                <ProgressBar theme={theme}>
                  <ProgressFill progress={profileData.overallProgress} theme={theme} />
                </ProgressBar>
                <ProgressText theme={theme}>
                  {profileData.completedStages} of {profileData.totalStages} stages completed
                </ProgressText>
              </OverallProgress>

              <SeasonProgress>
                <SeasonProgressItem>
                  <SeasonLabel theme={theme}>Spring</SeasonLabel>
                  <ProgressBar theme={theme} small>
                    <ProgressFill progress={profileData.springProgress} theme={theme} />
                  </ProgressBar>
                  <ProgressPercent theme={theme}>{profileData.springProgress}%</ProgressPercent>
                </SeasonProgressItem>
                <SeasonProgressItem>
                  <SeasonLabel theme={theme}>Summer</SeasonLabel>
                  <ProgressBar theme={theme} small>
                    <ProgressFill progress={profileData.summerProgress} theme={theme} />
                  </ProgressBar>
                  <ProgressPercent theme={theme}>{profileData.summerProgress}%</ProgressPercent>
                </SeasonProgressItem>
                <SeasonProgressItem>
                  <SeasonLabel theme={theme}>Autumn</SeasonLabel>
                  <ProgressBar theme={theme} small>
                    <ProgressFill progress={profileData.autumnProgress} theme={theme} />
                  </ProgressBar>
                  <ProgressPercent theme={theme}>{profileData.autumnProgress}%</ProgressPercent>
                </SeasonProgressItem>
                <SeasonProgressItem>
                  <SeasonLabel theme={theme}>Winter</SeasonLabel>
                  <ProgressBar theme={theme} small>
                    <ProgressFill progress={profileData.winterProgress} theme={theme} />
                  </ProgressBar>
                  <ProgressPercent theme={theme}>{profileData.winterProgress}%</ProgressPercent>
                </SeasonProgressItem>
              </SeasonProgress>
            </ProgressOverview>
          </CardContent>
        </ProfileCard>
      </DashboardGrid>
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background.default};
  min-height: 100vh;
`;

const Header = styled.div`
  margin-bottom: 32px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const Subtitle = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.1rem;
  margin: 0;
`;

const DashboardGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const ProfileCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.shadows.md};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  overflow: hidden;
`;

const CardHeader = styled.div`
  padding: 20px 24px 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const CardTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
`;

const CardContent = styled.div`
  padding: 24px;
`;

const ProfileInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InfoItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const InfoLabel = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-weight: 500;
`;

const InfoValue = styled.span`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 600;
`;

const SeasonBadge = styled.span<{ season: string }>`
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: ${({ season, theme }) => {
    switch (season) {
      case 'spring':
        return theme.colors.success.light;
      case 'summer':
        return theme.colors.warning.light;
      case 'autumn':
        return theme.colors.error.light;
      case 'winter':
        return theme.colors.info.light;
      default:
        return theme.colors.primary.light;
    }
  }};
  color: ${({ season, theme }) => {
    switch (season) {
      case 'spring':
        return theme.colors.success.dark;
      case 'summer':
        return theme.colors.warning.dark;
      case 'autumn':
        return theme.colors.error.dark;
      case 'winter':
        return theme.colors.info.dark;
      default:
        return theme.colors.primary.dark;
    }
  }};
`;

const FinancialMetrics = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const MetricItem = styled.div`
  text-align: center;
`;

const MetricLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  margin-bottom: 4px;
`;

const MetricValue = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
`;

const ProgressOverview = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

const OverallProgress = styled.div`
  text-align: center;
`;

const ProgressLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 600;
  margin-bottom: 8px;
`;

const ProgressBar = styled.div<{ small?: boolean }>`
  width: 100%;
  height: ${({ small }) => (small ? '8px' : '12px')};
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
`;

const ProgressFill = styled.div<{ progress: number }>`
  width: ${({ progress }) => progress}%;
  height: 100%;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.colors.primary.main},
    ${({ theme }) => theme.colors.primary.light}
  );
  transition: width 0.3s ease;
`;

const ProgressText = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
`;

const SeasonProgress = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const SeasonProgressItem = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const SeasonLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-weight: 500;
  min-width: 60px;
  font-size: 0.875rem;
`;

const ProgressPercent = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  min-width: 35px;
  text-align: right;
`;

export default LifeProfileDashboard;
