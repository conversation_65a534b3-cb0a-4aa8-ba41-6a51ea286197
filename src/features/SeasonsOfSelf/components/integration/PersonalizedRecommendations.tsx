/**
 * Personalized Recommendations Component
 *
 * This component generates personalized recommendations based on the user's
 * current life stage, financial situation, and progress across both frameworks.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';

interface PersonalizedRecommendationsProps {
  onTakeAction?: (actionId: string) => void;
}

interface Recommendation {
  id: string;
  title: string;
  description: string;
  category: 'immediate' | 'short-term' | 'long-term';
  priority: 'high' | 'medium' | 'low';
  type: 'financial' | 'life' | 'integration';
  estimatedTime: string;
  difficulty: 'easy' | 'moderate' | 'challenging';
  benefits: string[];
  actionSteps: string[];
  relatedAreas: string[];
}

interface RecommendationData {
  recommendations: Recommendation[];
  priorityActions: Recommendation[];
  quickWins: Recommendation[];
  longTermGoals: Recommendation[];
}

const PersonalizedRecommendations: React.FC<PersonalizedRecommendationsProps> = ({
  onTakeAction,
}) => {
  const { theme } = useTheme();
  const {
    stages,
    data: lifeData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
  } = useSeasonsOfSelf();
  const { data: financialData, getCompletionPercentage: getFinancialCompletion } =
    useFinancialCompass();

  const [recommendationData, setRecommendationData] = useState<RecommendationData>({
    recommendations: [],
    priorityActions: [],
    quickWins: [],
    longTermGoals: [],
  });

  const [selectedCategory, setSelectedCategory] = useState<
    'all' | 'immediate' | 'short-term' | 'long-term'
  >('all');

  // Generate personalized recommendations
  useEffect(() => {
    const generateRecommendations = () => {
      const recommendations: Recommendation[] = [];
      const lifeCompletion = getCompletionPercentage();
      const financialCompletion = getFinancialCompletion();

      // Current life stage analysis
      const currentStage = stages.find((stage) => !stage.completed) || stages[0];
      const currentSeason = currentStage?.season || 'spring';

      // Financial data analysis
      const hasPersonalInfo = financialData.north?.personalInformation?.firstName;
      const hasIncomeData = financialData.north?.incomeDetails?.primaryIncome;
      const hasRetirementPlan = financialData.east?.retirementGoals;
      const hasRiskAssessment = financialData.north?.riskAssessment?.riskScore;
      const hasInsurance = financialData.south?.insuranceNeeds;

      // Generate recommendations based on current state

      // Foundation Building (Spring Season)
      if (currentSeason === 'spring') {
        if (!hasPersonalInfo) {
          recommendations.push({
            id: 'complete-personal-info',
            title: 'Complete Your Personal Information',
            description:
              'Start your financial foundation by providing basic personal details. This enables personalized financial planning.',
            category: 'immediate',
            priority: 'high',
            type: 'financial',
            estimatedTime: '5 minutes',
            difficulty: 'easy',
            benefits: [
              'Enables personalized planning',
              'Foundation for all financial tools',
              'Better recommendations',
            ],
            actionSteps: [
              'Navigate to Financial Compass',
              'Complete Personal Information form',
              'Save your details',
            ],
            relatedAreas: ['Financial Foundation', 'Spring Season'],
          });
        }

        if (getSeasonCompletionPercentage('spring') < 50) {
          recommendations.push({
            id: 'complete-spring-assessments',
            title: 'Complete Spring Season Assessments',
            description:
              'Focus on pleasure and happiness assessments to build a strong foundation for your life journey.',
            category: 'immediate',
            priority: 'high',
            type: 'life',
            estimatedTime: '20 minutes',
            difficulty: 'easy',
            benefits: ['Clear foundation', 'Better self-awareness', 'Aligned life planning'],
            actionSteps: [
              'Complete Pleasure Stage assessment',
              'Work on Happiness Stage activities',
              'Reflect on your values',
            ],
            relatedAreas: ['Spring Season', 'Foundation Building'],
          });
        }
      }

      // Growth Phase (Summer Season)
      if (currentSeason === 'summer') {
        if (!hasRetirementPlan && hasIncomeData) {
          recommendations.push({
            id: 'start-retirement-planning',
            title: 'Begin Retirement Planning',
            description:
              'Your growth phase is perfect for establishing long-term financial goals. Start planning for retirement now.',
            category: 'short-term',
            priority: 'high',
            type: 'financial',
            estimatedTime: '30 minutes',
            difficulty: 'moderate',
            benefits: ['Long-term security', 'Compound growth', 'Peace of mind'],
            actionSteps: [
              'Set retirement age goal',
              'Calculate needed savings',
              'Choose investment strategy',
            ],
            relatedAreas: ['Summer Season', 'Retirement Planning'],
          });
        }

        if (getSeasonCompletionPercentage('summer') < 50) {
          recommendations.push({
            id: 'build-momentum',
            title: 'Build Life Momentum',
            description:
              'Focus on joy and momentum-building activities to accelerate your personal growth.',
            category: 'immediate',
            priority: 'medium',
            type: 'life',
            estimatedTime: '25 minutes',
            difficulty: 'moderate',
            benefits: ['Increased motivation', 'Better habits', 'Sustained growth'],
            actionSteps: [
              'Identify joy sources',
              'Create momentum practices',
              'Track your progress',
            ],
            relatedAreas: ['Summer Season', 'Personal Growth'],
          });
        }
      }

      // Integration Recommendations
      if (Math.abs(lifeCompletion - financialCompletion) > 30) {
        const lagginArea = lifeCompletion < financialCompletion ? 'life' : 'financial';
        recommendations.push({
          id: 'balance-progress',
          title: `Balance Your ${lagginArea === 'life' ? 'Life Journey' : 'Financial Planning'} Progress`,
          description: `Your ${lagginArea === 'life' ? 'life journey' : 'financial planning'} is lagging behind. Focus on this area to create better alignment.`,
          category: 'short-term',
          priority: 'high',
          type: 'integration',
          estimatedTime: '45 minutes',
          difficulty: 'moderate',
          benefits: ['Better alignment', 'Holistic progress', 'Reduced stress'],
          actionSteps: [
            `Complete more ${lagginArea === 'life' ? 'life journey' : 'financial'} assessments`,
            'Review your priorities',
            'Create an action plan',
          ],
          relatedAreas: ['Integration', 'Balance'],
        });
      }

      // Quick Wins
      if (!hasRiskAssessment) {
        recommendations.push({
          id: 'risk-assessment',
          title: 'Complete Risk Assessment',
          description:
            'Quick assessment to understand your risk tolerance and optimize your financial strategy.',
          category: 'immediate',
          priority: 'medium',
          type: 'financial',
          estimatedTime: '10 minutes',
          difficulty: 'easy',
          benefits: ['Better investment decisions', 'Aligned risk tolerance', 'Optimized returns'],
          actionSteps: ['Take risk assessment quiz', 'Review results', 'Adjust strategy if needed'],
          relatedAreas: ['Risk Management', 'Investment Planning'],
        });
      }

      // Long-term Goals
      if (lifeCompletion > 50 && financialCompletion > 50) {
        recommendations.push({
          id: 'legacy-planning',
          title: 'Begin Legacy Planning',
          description:
            'With strong foundations in place, start thinking about the legacy you want to leave behind.',
          category: 'long-term',
          priority: 'medium',
          type: 'integration',
          estimatedTime: '60 minutes',
          difficulty: 'challenging',
          benefits: ['Meaningful impact', 'Family security', 'Personal fulfillment'],
          actionSteps: [
            'Define your values',
            'Set legacy goals',
            'Create action plan',
            'Review regularly',
          ],
          relatedAreas: ['Legacy Planning', 'Winter Season', 'West Direction'],
        });
      }

      // Advanced Recommendations
      if (currentSeason === 'autumn' || currentSeason === 'winter') {
        recommendations.push({
          id: 'purpose-alignment',
          title: 'Align Purpose with Financial Goals',
          description:
            'Ensure your financial planning supports your deeper life purpose and calling.',
          category: 'long-term',
          priority: 'high',
          type: 'integration',
          estimatedTime: '90 minutes',
          difficulty: 'challenging',
          benefits: ['Meaningful wealth', 'Purpose-driven decisions', 'Life satisfaction'],
          actionSteps: [
            'Complete purpose assessment',
            'Review financial goals',
            'Align strategies',
            'Create integrated plan',
          ],
          relatedAreas: ['Purpose', 'Financial Goals', 'Integration'],
        });
      }

      // Categorize recommendations
      const priorityActions = recommendations.filter((r) => r.priority === 'high');
      const quickWins = recommendations.filter(
        (r) => r.category === 'immediate' && r.difficulty === 'easy'
      );
      const longTermGoals = recommendations.filter((r) => r.category === 'long-term');

      setRecommendationData({
        recommendations,
        priorityActions,
        quickWins,
        longTermGoals,
      });
    };

    generateRecommendations();
  }, [
    stages,
    lifeData,
    financialData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
    getFinancialCompletion,
  ]);

  const filteredRecommendations =
    selectedCategory === 'all'
      ? recommendationData.recommendations
      : recommendationData.recommendations.filter((r) => r.category === selectedCategory);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return theme.colors.error.main;
      case 'medium':
        return theme.colors.warning.main;
      case 'low':
        return theme.colors.success.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return '🟢';
      case 'moderate':
        return '🟡';
      case 'challenging':
        return '🔴';
      default:
        return '⚪';
    }
  };

  const handleTakeAction = (actionId: string) => {
    if (onTakeAction) {
      onTakeAction(actionId);
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Personalized Recommendations</Title>
        <Subtitle theme={theme}>Tailored action steps based on your unique journey</Subtitle>
      </Header>

      {/* Category Filter */}
      <FilterSection>
        <FilterButton
          active={selectedCategory === 'all'}
          onClick={() => setSelectedCategory('all')}
          theme={theme}
        >
          All Recommendations
        </FilterButton>
        <FilterButton
          active={selectedCategory === 'immediate'}
          onClick={() => setSelectedCategory('immediate')}
          theme={theme}
        >
          Immediate Actions
        </FilterButton>
        <FilterButton
          active={selectedCategory === 'short-term'}
          onClick={() => setSelectedCategory('short-term')}
          theme={theme}
        >
          Short-term Goals
        </FilterButton>
        <FilterButton
          active={selectedCategory === 'long-term'}
          onClick={() => setSelectedCategory('long-term')}
          theme={theme}
        >
          Long-term Vision
        </FilterButton>
      </FilterSection>

      {/* Quick Overview Cards */}
      <OverviewSection>
        <OverviewCard theme={theme}>
          <OverviewTitle theme={theme}>Priority Actions</OverviewTitle>
          <OverviewCount theme={theme}>{recommendationData.priorityActions.length}</OverviewCount>
        </OverviewCard>
        <OverviewCard theme={theme}>
          <OverviewTitle theme={theme}>Quick Wins</OverviewTitle>
          <OverviewCount theme={theme}>{recommendationData.quickWins.length}</OverviewCount>
        </OverviewCard>
        <OverviewCard theme={theme}>
          <OverviewTitle theme={theme}>Long-term Goals</OverviewTitle>
          <OverviewCount theme={theme}>{recommendationData.longTermGoals.length}</OverviewCount>
        </OverviewCard>
      </OverviewSection>

      {/* Recommendations List */}
      <RecommendationsSection>
        {filteredRecommendations.length > 0 ? (
          <RecommendationsList>
            {filteredRecommendations.map((recommendation) => (
              <RecommendationCard key={recommendation.id} theme={theme}>
                <RecommendationHeader>
                  <RecommendationTitle theme={theme}>{recommendation.title}</RecommendationTitle>
                  <RecommendationMeta>
                    <PriorityBadge priority={recommendation.priority} theme={theme}>
                      {recommendation.priority} priority
                    </PriorityBadge>
                    <DifficultyBadge>
                      {getDifficultyIcon(recommendation.difficulty)} {recommendation.difficulty}
                    </DifficultyBadge>
                    <TimeBadge theme={theme}>{recommendation.estimatedTime}</TimeBadge>
                  </RecommendationMeta>
                </RecommendationHeader>

                <RecommendationDescription theme={theme}>
                  {recommendation.description}
                </RecommendationDescription>

                <BenefitsList>
                  <BenefitsTitle theme={theme}>Benefits:</BenefitsTitle>
                  {recommendation.benefits.map((benefit, index) => (
                    <BenefitItem key={index} theme={theme}>
                      • {benefit}
                    </BenefitItem>
                  ))}
                </BenefitsList>

                <ActionSteps>
                  <StepsTitle theme={theme}>Action Steps:</StepsTitle>
                  <StepsList>
                    {recommendation.actionSteps.map((step, index) => (
                      <StepItem key={index} theme={theme}>
                        <StepNumber theme={theme}>{index + 1}</StepNumber>
                        <StepText theme={theme}>{step}</StepText>
                      </StepItem>
                    ))}
                  </StepsList>
                </ActionSteps>

                <RecommendationFooter>
                  <TypeBadge type={recommendation.type} theme={theme}>
                    {recommendation.type}
                  </TypeBadge>
                  <ActionButton onClick={() => handleTakeAction(recommendation.id)} theme={theme}>
                    Take Action
                  </ActionButton>
                </RecommendationFooter>
              </RecommendationCard>
            ))}
          </RecommendationsList>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>🎯</EmptyIcon>
            <EmptyText theme={theme}>
              No recommendations available for this category. Complete more assessments to get
              personalized suggestions.
            </EmptyText>
          </EmptyState>
        )}
      </RecommendationsSection>
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background.default};
  min-height: 100vh;
`;

const Header = styled.div`
  margin-bottom: 32px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const Subtitle = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.1rem;
  margin: 0;
`;

const FilterSection = styled.div`
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 32px;
  flex-wrap: wrap;
`;

const FilterButton = styled.button<{ active: boolean }>`
  padding: 8px 16px;
  border: 2px solid
    ${({ active, theme }) => (active ? theme.colors.primary.main : theme.colors.border.main)};
  background-color: ${({ active, theme }) =>
    active ? theme.colors.primary.main : theme.colors.background.paper};
  color: ${({ active, theme }) =>
    active ? theme.colors.primary.contrastText : theme.colors.text.primary};
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.main};
    background-color: ${({ active, theme }) =>
      active ? theme.colors.primary.dark : theme.colors.primary.light};
  }
`;

const OverviewSection = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
`;

const OverviewCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const OverviewTitle = styled.h4`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const OverviewCount = styled.div`
  color: ${({ theme }) => theme.colors.primary.main};
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
`;

const RecommendationsSection = styled.div`
  max-width: 900px;
  margin: 0 auto;
`;

const RecommendationsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const RecommendationCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: 24px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const RecommendationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  gap: 16px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
`;

const RecommendationTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
`;

const RecommendationMeta = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const PriorityBadge = styled.span<{ priority: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${({ priority, theme }) => {
    switch (priority) {
      case 'high':
        return theme.colors.error.light;
      case 'medium':
        return theme.colors.warning.light;
      case 'low':
        return theme.colors.success.light;
      default:
        return theme.colors.background.secondary;
    }
  }};
  color: ${({ priority, theme }) => {
    switch (priority) {
      case 'high':
        return theme.colors.error.dark;
      case 'medium':
        return theme.colors.warning.dark;
      case 'low':
        return theme.colors.success.dark;
      default:
        return theme.colors.text.secondary;
    }
  }};
`;

const DifficultyBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const TimeBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ theme }) => theme.colors.info.light};
  color: ${({ theme }) => theme.colors.info.dark};
`;

const RecommendationDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0 0 20px 0;
  line-height: 1.6;
`;

const BenefitsList = styled.div`
  margin-bottom: 20px;
`;

const BenefitsTitle = styled.h5`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const BenefitItem = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  margin-bottom: 4px;
`;

const ActionSteps = styled.div`
  margin-bottom: 20px;
`;

const StepsTitle = styled.h5`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const StepsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const StepItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 12px;
`;

const StepNumber = styled.div`
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
`;

const StepText = styled.span`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  line-height: 1.5;
`;

const RecommendationFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;

  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
`;

const TypeBadge = styled.span<{ type: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ type, theme }) => {
    switch (type) {
      case 'financial':
        return theme.colors.info.light;
      case 'life':
        return theme.colors.success.light;
      case 'integration':
        return theme.colors.primary.light;
      default:
        return theme.colors.background.secondary;
    }
  }};
  color: ${({ type, theme }) => {
    switch (type) {
      case 'financial':
        return theme.colors.info.dark;
      case 'life':
        return theme.colors.success.dark;
      case 'integration':
        return theme.colors.primary.dark;
      default:
        return theme.colors.text.secondary;
    }
  }};
`;

const ActionButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px 24px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.1rem;
  margin: 0;
  line-height: 1.5;
`;

export default PersonalizedRecommendations;
