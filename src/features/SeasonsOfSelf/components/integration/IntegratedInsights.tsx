/**
 * Integrated Insights Component
 *
 * This component provides cross-framework insights by analyzing data from both
 * Financial Compass and Life Journey to identify patterns, correlations, and opportunities.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import { useFinancialCompass } from '../../../FinancialCompass/context/FinancialCompassContext';

interface IntegratedInsightsProps {
  onViewRecommendations?: () => void;
}

interface Insight {
  id: string;
  type: 'correlation' | 'gap' | 'opportunity' | 'warning';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: 'financial' | 'life' | 'integration';
  actionable: boolean;
  relatedAreas: string[];
}

interface InsightData {
  alignmentScore: number;
  insights: Insight[];
  correlations: {
    lifeStageFinancialAlignment: number;
    progressConsistency: number;
    priorityAlignment: number;
  };
  gaps: {
    financialGaps: string[];
    lifeGaps: string[];
    integrationGaps: string[];
  };
  opportunities: string[];
}

const IntegratedInsights: React.FC<IntegratedInsightsProps> = ({ onViewRecommendations }) => {
  const { theme } = useTheme();
  const {
    stages,
    data: lifeData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
  } = useSeasonsOfSelf();
  const { data: financialData, getCompletionPercentage: getFinancialCompletion } =
    useFinancialCompass();

  const [insightData, setInsightData] = useState<InsightData>({
    alignmentScore: 0,
    insights: [],
    correlations: {
      lifeStageFinancialAlignment: 0,
      progressConsistency: 0,
      priorityAlignment: 0,
    },
    gaps: {
      financialGaps: [],
      lifeGaps: [],
      integrationGaps: [],
    },
    opportunities: [],
  });

  // Generate insights based on cross-framework analysis
  useEffect(() => {
    const generateInsights = () => {
      const insights: Insight[] = [];
      const lifeCompletion = getCompletionPercentage();
      const financialCompletion = getFinancialCompletion();

      // Calculate correlations
      const progressDifference = Math.abs(lifeCompletion - financialCompletion);
      const progressConsistency = Math.max(0, 100 - progressDifference);

      // Life stage analysis
      const currentStage = stages.find((stage) => !stage.completed) || stages[0];
      const currentSeason = currentStage?.season || 'spring';

      // Financial data analysis
      const hasPersonalInfo = financialData.north?.personalInformation?.firstName;
      const hasIncomeData = financialData.north?.incomeDetails?.primaryIncome;
      const hasRetirementPlan = financialData.east?.retirementGoals;
      const hasRiskAssessment = financialData.north?.riskAssessment?.riskScore;

      // Generate specific insights

      // Progress Alignment Insight
      if (progressDifference > 30) {
        insights.push({
          id: 'progress-misalignment',
          type: 'gap',
          title: 'Progress Misalignment Detected',
          description: `Your life journey progress (${lifeCompletion}%) and financial planning progress (${financialCompletion}%) are significantly different. Consider balancing your focus.`,
          impact: 'high',
          category: 'integration',
          actionable: true,
          relatedAreas: ['Life Planning', 'Financial Planning'],
        });
      }

      // Life Stage Financial Alignment
      if (currentSeason === 'spring' && !hasIncomeData) {
        insights.push({
          id: 'spring-financial-foundation',
          type: 'opportunity',
          title: 'Foundation Building Opportunity',
          description:
            "You're in the Spring season focused on foundation building. Completing your financial assessment would align perfectly with this life stage.",
          impact: 'high',
          category: 'integration',
          actionable: true,
          relatedAreas: ['Spring Season', 'Financial Foundation'],
        });
      }

      if (currentSeason === 'summer' && !hasRetirementPlan) {
        insights.push({
          id: 'summer-growth-planning',
          type: 'opportunity',
          title: 'Growth Phase Planning',
          description:
            "You're in the Summer season of growth and momentum. This is an ideal time to establish your retirement planning strategy.",
          impact: 'medium',
          category: 'integration',
          actionable: true,
          relatedAreas: ['Summer Season', 'Retirement Planning'],
        });
      }

      // Risk Assessment Correlation
      if (hasRiskAssessment && currentSeason) {
        const riskScore = financialData.north?.riskAssessment?.riskScore || 0;
        if (currentSeason === 'spring' && riskScore > 7) {
          insights.push({
            id: 'spring-high-risk',
            type: 'warning',
            title: 'Risk Tolerance Mismatch',
            description:
              'Your high risk tolerance may not align with the foundation-building focus of the Spring season. Consider a more balanced approach.',
            impact: 'medium',
            category: 'correlation',
            actionable: true,
            relatedAreas: ['Risk Assessment', 'Spring Season'],
          });
        }
      }

      // Completion Consistency
      if (lifeCompletion > 70 && financialCompletion < 30) {
        insights.push({
          id: 'financial-lagging',
          type: 'gap',
          title: 'Financial Planning Lagging',
          description:
            'Your life journey is well-developed, but your financial planning needs attention to support your life goals.',
          impact: 'high',
          category: 'financial',
          actionable: true,
          relatedAreas: ['Financial Planning', 'Goal Alignment'],
        });
      }

      if (financialCompletion > 70 && lifeCompletion < 30) {
        insights.push({
          id: 'life-planning-lagging',
          type: 'gap',
          title: 'Life Planning Opportunity',
          description:
            'Your financial foundation is strong. Developing your life journey framework could provide direction for your financial goals.',
          impact: 'medium',
          category: 'life',
          actionable: true,
          relatedAreas: ['Life Planning', 'Purpose Alignment'],
        });
      }

      // Positive correlations
      if (progressDifference < 15 && lifeCompletion > 50) {
        insights.push({
          id: 'strong-alignment',
          type: 'correlation',
          title: 'Strong Life-Financial Alignment',
          description:
            'Excellent! Your life journey and financial planning are well-aligned, creating a solid foundation for achieving your goals.',
          impact: 'high',
          category: 'integration',
          actionable: false,
          relatedAreas: ['Integration', 'Goal Achievement'],
        });
      }

      // Calculate overall alignment score
      const alignmentScore = Math.round(
        (progressConsistency + Math.min(lifeCompletion, financialCompletion)) / 2
      );

      // Identify gaps and opportunities
      const gaps = {
        financialGaps: [],
        lifeGaps: [],
        integrationGaps: [],
      };

      const opportunities = [];

      if (!hasPersonalInfo) gaps.financialGaps.push('Personal Information');
      if (!hasIncomeData) gaps.financialGaps.push('Income Assessment');
      if (!hasRetirementPlan) gaps.financialGaps.push('Retirement Planning');

      if (getSeasonCompletionPercentage('spring') < 50) gaps.lifeGaps.push('Foundation Building');
      if (getSeasonCompletionPercentage('summer') < 50) gaps.lifeGaps.push('Growth & Momentum');

      if (progressDifference > 20) gaps.integrationGaps.push('Progress Alignment');

      // Opportunities based on current state
      if (currentSeason === 'spring' && hasIncomeData) {
        opportunities.push('Optimize savings rate during foundation phase');
      }
      if (currentSeason === 'summer' && hasRetirementPlan) {
        opportunities.push('Accelerate retirement contributions during growth phase');
      }

      setInsightData({
        alignmentScore,
        insights,
        correlations: {
          lifeStageFinancialAlignment: alignmentScore,
          progressConsistency,
          priorityAlignment: Math.min(lifeCompletion, financialCompletion),
        },
        gaps,
        opportunities,
      });
    };

    generateInsights();
  }, [
    stages,
    lifeData,
    financialData,
    getCompletionPercentage,
    getSeasonCompletionPercentage,
    getFinancialCompletion,
  ]);

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'correlation':
        return '🔗';
      case 'gap':
        return '⚠️';
      case 'opportunity':
        return '💡';
      case 'warning':
        return '🚨';
      default:
        return '📊';
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return theme.colors.error.main;
      case 'medium':
        return theme.colors.warning.main;
      case 'low':
        return theme.colors.success.main;
      default:
        return theme.colors.text.secondary;
    }
  };

  return (
    <Container theme={theme}>
      <Header theme={theme}>
        <Title theme={theme}>Integrated Insights</Title>
        <Subtitle theme={theme}>
          Cross-framework analysis of your life and financial journey
        </Subtitle>
      </Header>

      {/* Alignment Score */}
      <AlignmentCard theme={theme}>
        <CardHeader theme={theme}>
          <CardTitle theme={theme}>Overall Alignment Score</CardTitle>
        </CardHeader>
        <CardContent>
          <AlignmentScore>
            <ScoreCircle score={insightData.alignmentScore} theme={theme}>
              <ScoreNumber theme={theme}>{insightData.alignmentScore}</ScoreNumber>
              <ScoreLabel theme={theme}>/ 100</ScoreLabel>
            </ScoreCircle>
            <ScoreDescription theme={theme}>
              {insightData.alignmentScore >= 80 &&
                'Excellent alignment between your life journey and financial planning!'}
              {insightData.alignmentScore >= 60 &&
                insightData.alignmentScore < 80 &&
                'Good alignment with room for improvement.'}
              {insightData.alignmentScore >= 40 &&
                insightData.alignmentScore < 60 &&
                'Moderate alignment. Consider focusing on integration.'}
              {insightData.alignmentScore < 40 &&
                'Significant opportunity to align your life and financial planning.'}
            </ScoreDescription>
          </AlignmentScore>
        </CardContent>
      </AlignmentCard>

      {/* Insights List */}
      <InsightsSection>
        <SectionTitle theme={theme}>Key Insights</SectionTitle>
        {insightData.insights.length > 0 ? (
          <InsightsList>
            {insightData.insights.map((insight) => (
              <InsightCard key={insight.id} theme={theme}>
                <InsightHeader>
                  <InsightIcon>{getInsightIcon(insight.type)}</InsightIcon>
                  <InsightTitle theme={theme}>{insight.title}</InsightTitle>
                  <ImpactBadge impact={insight.impact} theme={theme}>
                    {insight.impact} impact
                  </ImpactBadge>
                </InsightHeader>
                <InsightDescription theme={theme}>{insight.description}</InsightDescription>
                <InsightFooter>
                  <CategoryBadge category={insight.category} theme={theme}>
                    {insight.category}
                  </CategoryBadge>
                  {insight.actionable && (
                    <ActionableBadge theme={theme}>Actionable</ActionableBadge>
                  )}
                </InsightFooter>
              </InsightCard>
            ))}
          </InsightsList>
        ) : (
          <EmptyState theme={theme}>
            <EmptyIcon>🔍</EmptyIcon>
            <EmptyText theme={theme}>
              Complete more assessments to generate personalized insights
            </EmptyText>
          </EmptyState>
        )}
      </InsightsSection>

      {/* Action Button */}
      {onViewRecommendations && (
        <ActionSection>
          <ActionButton onClick={onViewRecommendations} theme={theme}>
            View Personalized Recommendations
          </ActionButton>
        </ActionSection>
      )}
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  padding: 24px;
  background-color: ${({ theme }) => theme.colors.background.default};
  min-height: 100vh;
`;

const Header = styled.div`
  margin-bottom: 32px;
  text-align: center;
`;

const Title = styled.h1`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 8px;
`;

const Subtitle = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.1rem;
  margin: 0;
`;

const AlignmentCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.shadows.md};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  margin-bottom: 32px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const CardHeader = styled.div`
  padding: 20px 24px 16px;
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  text-align: center;
`;

const CardTitle = styled.h3`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
`;

const CardContent = styled.div`
  padding: 32px 24px;
`;

const AlignmentScore = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
`;

const ScoreCircle = styled.div<{ score: number }>`
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: conic-gradient(
    ${({ theme }) => theme.colors.primary.main} ${({ score }) => score * 3.6}deg,
    ${({ theme }) => theme.colors.background.secondary} ${({ score }) => score * 3.6}deg
  );
  position: relative;

  &::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background-color: ${({ theme }) => theme.colors.background.paper};
  }
`;

const ScoreNumber = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 2rem;
  font-weight: 700;
  z-index: 1;
`;

const ScoreLabel = styled.div`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 0.875rem;
  z-index: 1;
`;

const ScoreDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
`;

const InsightsSection = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const SectionTitle = styled.h2`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
`;

const InsightsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const InsightCard = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: 20px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const InsightHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
`;

const InsightIcon = styled.span`
  font-size: 1.25rem;
`;

const InsightTitle = styled.h4`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
  flex: 1;
`;

const ImpactBadge = styled.span<{ impact: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  background-color: ${({ impact, theme }) => {
    switch (impact) {
      case 'high':
        return theme.colors.error.light;
      case 'medium':
        return theme.colors.warning.light;
      case 'low':
        return theme.colors.success.light;
      default:
        return theme.colors.background.secondary;
    }
  }};
  color: ${({ impact, theme }) => {
    switch (impact) {
      case 'high':
        return theme.colors.error.dark;
      case 'medium':
        return theme.colors.warning.dark;
      case 'low':
        return theme.colors.success.dark;
      default:
        return theme.colors.text.secondary;
    }
  }};
`;

const InsightDescription = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  margin: 0 0 16px 0;
  line-height: 1.5;
`;

const InsightFooter = styled.div`
  display: flex;
  gap: 8px;
  align-items: center;
`;

const CategoryBadge = styled.span<{ category: string }>`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ category, theme }) => {
    switch (category) {
      case 'financial':
        return theme.colors.info.light;
      case 'life':
        return theme.colors.success.light;
      case 'integration':
        return theme.colors.primary.light;
      default:
        return theme.colors.background.secondary;
    }
  }};
  color: ${({ category, theme }) => {
    switch (category) {
      case 'financial':
        return theme.colors.info.dark;
      case 'life':
        return theme.colors.success.dark;
      case 'integration':
        return theme.colors.primary.dark;
      default:
        return theme.colors.text.secondary;
    }
  }};
`;

const ActionableBadge = styled.span`
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: ${({ theme }) => theme.colors.warning.light};
  color: ${({ theme }) => theme.colors.warning.dark};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 48px 24px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 16px;
`;

const EmptyText = styled.p`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 1.1rem;
  margin: 0;
`;

const ActionSection = styled.div`
  text-align: center;
  margin-top: 32px;
`;

const ActionButton = styled.button`
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

export default IntegratedInsights;
