/**
 * Purpose Stage Component
 *
 * This component represents the second stage of the Winter season,
 * focusing on aligning one's life with their deeper purpose.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface PurposeStageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Purpose alignment type
interface PurposeAlignment {
  id: string;
  area: string;
  currentAlignment: number;
  desiredAlignment: number;
  gap: string;
  alignmentActions: string[];
}

// Life areas
const lifeAreas = [
  { id: 'career', name: 'Career & Work' },
  { id: 'relationships', name: 'Relationships' },
  { id: 'health', name: 'Health & Wellbeing' },
  { id: 'finances', name: 'Finances' },
  { id: 'personal_growth', name: 'Personal Growth' },
  { id: 'spirituality', name: 'Spirituality & Purpose' },
  { id: 'lifestyle', name: 'Lifestyle & Environment' },
  { id: 'community', name: 'Community & Contribution' },
];

/**
 * Purpose Stage Component
 */
export const PurposeStage: React.FC<PurposeStageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // Get saved data or initialize with defaults
  const [alignments, setAlignments] = useState<PurposeAlignment[]>(
    data.winter && data.winter.purposeAlignments ? data.winter.purposeAlignments : []
  );

  const [newAlignment, setNewAlignment] = useState<PurposeAlignment>({
    id: '',
    area: '',
    currentAlignment: 5,
    desiredAlignment: 10,
    gap: '',
    alignmentActions: [],
  });

  const [newAction, setNewAction] = useState('');

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'winter_purpose_alignments',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when alignments change
  useEffect(() => {
    updateData('winter', 'purposeAlignments', alignments);

    // Auto-save data
    autoSave.save(alignments);

    // Mark stage as completed if at least 3 alignments are added
    const purposeStage = stages.find((stage) => stage.id === 'purpose');
    if (purposeStage && !purposeStage.completed && alignments.length >= 3) {
      updateStageCompletion('purpose', true);
    } else if (purposeStage && purposeStage.completed && alignments.length < 3) {
      updateStageCompletion('purpose', false);
    }
  }, [alignments, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewAlignment((prev) => ({
      ...prev,
      [name]:
        name === 'currentAlignment' || name === 'desiredAlignment' ? parseInt(value) || 0 : value,
    }));
  };

  // Handle action input change
  const handleActionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewAction(e.target.value);
  };

  // Handle adding an action
  const handleAddAction = () => {
    if (!newAction.trim()) return;

    setNewAlignment((prev) => ({
      ...prev,
      alignmentActions: [...prev.alignmentActions, newAction.trim()],
    }));

    setNewAction('');
  };

  // Handle removing an action
  const handleRemoveAction = (index: number) => {
    setNewAlignment((prev) => ({
      ...prev,
      alignmentActions: prev.alignmentActions.filter((_, i) => i !== index),
    }));
  };

  // Handle adding a new alignment
  const handleAddAlignment = () => {
    if (!newAlignment.area || !newAlignment.gap || newAlignment.alignmentActions.length === 0)
      return;

    const newItem = {
      ...newAlignment,
      id: `purpose-${Date.now()}`,
    };

    setAlignments((prev) => [...prev, newItem]);
    setNewAlignment({
      id: '',
      area: '',
      currentAlignment: 5,
      desiredAlignment: 10,
      gap: '',
      alignmentActions: [],
    });
  };

  // Handle removing an alignment
  const handleRemoveAlignment = (id: string) => {
    setAlignments((prev) => prev.filter((alignment) => alignment.id !== id));
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Purpose Stage</StageTitle>
          <StageDescription>
            The Purpose Stage is about aligning your life with your deeper purpose. Assess how
            different areas of your life currently align with your purpose and identify actions to
            increase alignment.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Purpose Alignment</SectionTitle>
            <SectionDescription>
              For each area of your life, assess how aligned it currently is with your purpose, and
              identify specific actions to increase that alignment.
            </SectionDescription>

            <AlignmentForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="area">Life Area</Label>
                  <Select
                    id="area"
                    name="area"
                    value={newAlignment.area}
                    onChange={handleInputChange}
                  >
                    <option value="">Select life area</option>
                    {lifeAreas.map((area) => (
                      <option key={area.id} value={area.id}>
                        {area.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="currentAlignment">Current Alignment (1-10)</Label>
                  <SliderContainer>
                    <Slider
                      id="currentAlignment"
                      name="currentAlignment"
                      type="range"
                      min="1"
                      max="10"
                      value={newAlignment.currentAlignment}
                      onChange={handleInputChange}
                    />
                    <SliderValue>{newAlignment.currentAlignment}</SliderValue>
                  </SliderContainer>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="desiredAlignment">Desired Alignment (1-10)</Label>
                  <SliderContainer>
                    <Slider
                      id="desiredAlignment"
                      name="desiredAlignment"
                      type="range"
                      min="1"
                      max="10"
                      value={newAlignment.desiredAlignment}
                      onChange={handleInputChange}
                    />
                    <SliderValue>{newAlignment.desiredAlignment}</SliderValue>
                  </SliderContainer>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="gap">Gap Description</Label>
                  <TextArea
                    id="gap"
                    name="gap"
                    value={newAlignment.gap}
                    onChange={handleInputChange}
                    placeholder="Describe the gap between your current and desired alignment with your purpose in this area"
                    rows={3}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label>Alignment Actions</Label>
                  <ActionInputGroup>
                    <ActionInput
                      value={newAction}
                      onChange={handleActionChange}
                      placeholder="Add an action to increase alignment"
                    />
                    <ActionButton onClick={handleAddAction}>Add</ActionButton>
                  </ActionInputGroup>

                  <ActionsList>
                    {newAlignment.alignmentActions.map((action, index) => (
                      <ActionItem key={index}>
                        <ActionText>{action}</ActionText>
                        <ActionRemove onClick={() => handleRemoveAction(index)}>✕</ActionRemove>
                      </ActionItem>
                    ))}
                  </ActionsList>
                </FormGroup>
              </FormRow>

              <AddButton
                onClick={handleAddAlignment}
                disabled={
                  !newAlignment.area ||
                  !newAlignment.gap ||
                  newAlignment.alignmentActions.length === 0
                }
              >
                Add Purpose Alignment
              </AddButton>
            </AlignmentForm>

            <AlignmentsList>
              {alignments.length === 0 ? (
                <EmptyState>
                  No purpose alignments added yet. Add your first alignment above.
                </EmptyState>
              ) : (
                alignments.map((alignment) => (
                  <AlignmentItem key={alignment.id}>
                    <AlignmentHeader>
                      <AlignmentArea>
                        {lifeAreas.find((area) => area.id === alignment.area)?.name ||
                          alignment.area}
                      </AlignmentArea>
                      <AlignmentScores>
                        <AlignmentScore>Current: {alignment.currentAlignment}/10</AlignmentScore>
                        <AlignmentScore>Desired: {alignment.desiredAlignment}/10</AlignmentScore>
                      </AlignmentScores>
                      <RemoveButton onClick={() => handleRemoveAlignment(alignment.id)}>
                        ✕
                      </RemoveButton>
                    </AlignmentHeader>

                    <AlignmentDetails>
                      <AlignmentGap>
                        <GapLabel>Gap:</GapLabel> {alignment.gap}
                      </AlignmentGap>

                      <AlignmentActions>
                        <ActionsLabel>Actions:</ActionsLabel>
                        <ActionsList>
                          {alignment.alignmentActions.map((action, index) => (
                            <ActionListItem key={index}>{action}</ActionListItem>
                          ))}
                        </ActionsList>
                      </AlignmentActions>
                    </AlignmentDetails>
                  </AlignmentItem>
                ))
              )}
            </AlignmentsList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="outlined" onClick={handleBack}>
            Back to Calling Stage
          </Button>
          <Button variant="primary" onClick={handleComplete} disabled={alignments.length < 3}>
            {alignments.length < 3
              ? `Add ${3 - alignments.length} more ${alignments.length === 2 ? 'alignment' : 'alignments'} to continue`
              : 'Continue to Fulfillment Stage'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const AlignmentForm = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 600px) {
    flex-direction: column;
  }
`;

const FormGroup = styled.div<{ fullWidth?: boolean }>`
  flex: ${(props) => (props.fullWidth ? 1 : 0.5)};

  @media (max-width: 600px) {
    flex: 1;
  }
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const SliderContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const Slider = styled.input`
  flex: 1;
  -webkit-appearance: none;
  width: 100%;
  height: 8px;
  border-radius: 4px;
  background: ${({ theme }) => theme.colors.background.secondary};
  outline: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors.primary.main};
    cursor: pointer;
  }

  &::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors.primary.main};
    cursor: pointer;
  }
`;

const SliderValue = styled.span`
  width: 30px;
  text-align: center;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const ActionInputGroup = styled.div`
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
`;

const ActionInput = styled.input`
  flex: 1;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const ActionButton = styled(Button)`
  padding: 8px 16px;
`;

const ActionsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const ActionItem = styled.div`
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 4px;
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const ActionText = styled.span`
  flex: 1;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ActionRemove = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;

  &:hover {
    color: ${({ theme }) => theme.colors.error.main};
  }
`;

const AddButton = styled(Button)`
  margin-top: 8px;
`;

const AlignmentsList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const AlignmentItem = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border-left: 4px solid ${({ theme }) => theme.colors.primary.main};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const AlignmentHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 12px;
`;

const AlignmentArea = styled.h4`
  margin: 0;
  flex: 1;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const AlignmentScores = styled.div`
  display: flex;
  gap: 16px;
  margin-right: 16px;
`;

const AlignmentScore = styled.span`
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 4px;
  font-size: 0.75rem;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.error.main};
  }
`;

const AlignmentDetails = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const AlignmentGap = styled.div`
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.text.secondary};
`;

const GapLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const AlignmentActions = styled.div`
  font-size: 0.875rem;
`;

const ActionsLabel = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const ActionListItem = styled.li`
  margin-bottom: 4px;
`;

const EmptyState = styled.div`
  padding: 24px;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  text-align: center;
  color: ${({ theme }) => theme.colors.success.main};
  margin-top: 16px;
`;
