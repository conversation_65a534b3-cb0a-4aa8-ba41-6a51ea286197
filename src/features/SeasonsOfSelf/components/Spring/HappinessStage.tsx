/**
 * Happiness Stage Component
 *
 * This component represents the second stage of the Spring season,
 * focusing on building positive emotions and experiences.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';
import { MindfulnessExercises } from './MindfulnessExercises';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface HappinessStageProps {
  onComplete?: () => void;
  onBack?: () => void;
}

// Happiness practice type
interface HappinessPractice {
  id: string;
  practice: string;
  category: string;
  impact: number;
  consistency: string;
  notes: string;
}

// Happiness categories
const happinessCategories = [
  {
    id: 'gratitude',
    name: 'Gratitude',
    description: 'Practices that cultivate appreciation and thankfulness',
  },
  {
    id: 'connection',
    name: 'Social Connection',
    description: 'Activities that strengthen relationships with others',
  },
  {
    id: 'mindfulness',
    name: 'Mindfulness',
    description: 'Practices that bring awareness to the present moment',
  },
  {
    id: 'kindness',
    name: 'Acts of Kindness',
    description: 'Actions that benefit others and create positive feelings',
  },
  {
    id: 'growth',
    name: 'Personal Growth',
    description: 'Activities that develop skills and strengths',
  },
  {
    id: 'other',
    name: 'Other',
    description: 'Other happiness-building practices',
  },
];

/**
 * Happiness Stage Component
 */
export const HappinessStage: React.FC<HappinessStageProps> = ({ onComplete, onBack }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // State for choosing assessment method
  const [assessmentMethod, setAssessmentMethod] = useState<
    'choose' | 'traditional' | 'mindfulness'
  >('choose');

  // Get saved data or initialize with defaults
  const [practices, setPractices] = useState<HappinessPractice[]>(
    data.spring && data.spring.happinessPractices ? data.spring.happinessPractices : []
  );

  const [newPractice, setNewPractice] = useState<HappinessPractice>({
    id: '',
    practice: '',
    category: '',
    impact: 0,
    consistency: '',
    notes: '',
  });

  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Initialize auto-save
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'spring-happiness-stage',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when practices change
  useEffect(() => {
    updateData('spring', 'happinessPractices', practices);

    // Auto-save data
    autoSave.save(practices);

    // Mark stage as completed if at least 3 practices are added
    const happinessStage = stages.find((stage) => stage.id === 'happiness');
    if (happinessStage && !happinessStage.completed && practices.length >= 3) {
      updateStageCompletion('happiness', true);
    } else if (happinessStage && happinessStage.completed && practices.length < 3) {
      updateStageCompletion('happiness', false);
    }
  }, [practices, updateData, stages, autoSave, updateStageCompletion]);

  // Handle adding a new practice
  const handleAddPractice = () => {
    if (!newPractice.practice.trim() || !newPractice.category) return;

    const practiceToAdd = {
      ...newPractice,
      id: Date.now().toString(),
    };

    setPractices([...practices, practiceToAdd]);
    setNewPractice({
      id: '',
      practice: '',
      category: '',
      impact: 0,
      consistency: '',
      notes: '',
    });
  };

  // Handle removing a practice
  const handleRemovePractice = (id: string) => {
    setPractices(practices.filter((practice) => practice.id !== id));
  };

  // Handle input changes for new practice
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewPractice({
      ...newPractice,
      [name]: name === 'impact' ? parseInt(value, 10) : value,
    });
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  // Check if user has existing data to determine initial method
  useEffect(() => {
    if (data.spring?.mindfulnessData) {
      setAssessmentMethod('mindfulness');
    } else if (data.spring?.happinessPractices && data.spring.happinessPractices.length > 0) {
      setAssessmentMethod('traditional');
    }
  }, [data.spring]);

  // Render method selection
  if (assessmentMethod === 'choose') {
    return (
      <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
        <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
          <CardHeader>
            <StageTitle>Happiness Stage</StageTitle>
            <StageDescription>
              Choose your preferred method to build positive emotions and well-being.
            </StageDescription>
          </CardHeader>

          <CardContent>
            <MethodSelection>
              <MethodOption onClick={() => setAssessmentMethod('mindfulness')}>
                <MethodTitle>🧘 Mindfulness & Present-Moment Practices</MethodTitle>
                <MethodDescription>
                  Explore guided mindfulness exercises, gratitude practices, and reflection prompts
                  to enhance present-moment awareness.
                </MethodDescription>
                <MethodBadge>Recommended</MethodBadge>
              </MethodOption>

              <MethodOption onClick={() => setAssessmentMethod('traditional')}>
                <MethodTitle>😊 Happiness Practices</MethodTitle>
                <MethodDescription>
                  Identify and track specific practices that build positive emotions and contribute
                  to long-term happiness.
                </MethodDescription>
              </MethodOption>
            </MethodSelection>
          </CardContent>
        </StageCard>
      </Container>
    );
  }

  // Render Mindfulness Exercises
  if (assessmentMethod === 'mindfulness') {
    return (
      <MindfulnessExercises
        onComplete={onComplete}
        onBack={() => setAssessmentMethod('choose')}
        initialData={data.spring?.mindfulnessData}
      />
    );
  }

  // Render traditional happiness practices
  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Happiness Stage</StageTitle>
          <StageDescription>
            The Happiness Stage is about building positive emotions and experiences. Identify
            practices that contribute to your long-term happiness and well-being.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Happiness Practices</SectionTitle>
            <SectionDescription>
              List practices that build positive emotions and contribute to your long-term
              happiness. These could include gratitude journaling, meditation, connecting with loved
              ones, or learning new skills.
            </SectionDescription>

            <CategoriesGrid>
              {happinessCategories.map((category) => (
                <CategoryCard key={category.id}>
                  <CategoryName>{category.name}</CategoryName>
                  <CategoryDescription>{category.description}</CategoryDescription>
                </CategoryCard>
              ))}
            </CategoriesGrid>

            <ActivityForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="practice">Happiness Practice</Label>
                  <Input
                    id="practice"
                    name="practice"
                    value={newPractice.practice}
                    onChange={handleInputChange}
                    placeholder="e.g., Daily gratitude journaling"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    id="category"
                    name="category"
                    value={newPractice.category}
                    onChange={handleInputChange}
                  >
                    <option value="">Select category</option>
                    {happinessCategories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="impact">Impact Level (1-10)</Label>
                  <Input
                    id="impact"
                    name="impact"
                    type="number"
                    min="1"
                    max="10"
                    value={newPractice.impact || ''}
                    onChange={handleInputChange}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="consistency">Consistency</Label>
                  <Select
                    id="consistency"
                    name="consistency"
                    value={newPractice.consistency}
                    onChange={handleInputChange}
                  >
                    <option value="">Select consistency</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="occasionally">Occasionally</option>
                    <option value="not-yet">Not yet started</option>
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="notes">Notes</Label>
                  <TextArea
                    id="notes"
                    name="notes"
                    value={newPractice.notes}
                    onChange={handleInputChange}
                    placeholder="Any additional thoughts..."
                  />
                </FormGroup>
              </FormRow>

              <AddButton onClick={handleAddPractice}>Add Happiness Practice</AddButton>
            </ActivityForm>

            <PracticesList>
              {practices.length === 0 ? (
                <EmptyState>
                  No happiness practices added yet. Add your first practice above.
                </EmptyState>
              ) : (
                practices.map((practice) => (
                  <PracticeItem key={practice.id}>
                    <PracticeHeader>
                      <PracticeName>{practice.practice}</PracticeName>
                      <PracticeCategory>
                        {happinessCategories.find((c) => c.id === practice.category)?.name ||
                          practice.category}
                      </PracticeCategory>
                      <RemoveButton onClick={() => handleRemovePractice(practice.id)}>
                        ✕
                      </RemoveButton>
                    </PracticeHeader>
                    <PracticeDetails>
                      <PracticeImpact>Impact: {practice.impact}/10</PracticeImpact>
                      <PracticeConsistency>Consistency: {practice.consistency}</PracticeConsistency>
                      {practice.notes && <PracticeNotes>{practice.notes}</PracticeNotes>}
                    </PracticeDetails>
                  </PracticeItem>
                ))
              )}
            </PracticesList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="outlined" onClick={handleBack}>
            Back to Pleasure Stage
          </Button>
          <Button variant="primary" onClick={handleComplete} disabled={practices.length < 3}>
            {practices.length < 3
              ? `Add ${3 - practices.length} more ${practices.length === 2 ? 'practice' : 'practices'} to continue`
              : 'Complete Spring Season'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CategoriesGrid = styled.div`
display: grid;
grid - template - columns: repeat(auto - fill, minmax(250px, 1fr));
gap: 16px;
margin - bottom: 24px;
`;

const CategoryCard = styled.div`
padding: 16px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 8px;
box - shadow: ${({ theme }) => theme.shadows.sm};
border - left: 3px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CategoryName = styled.h4`
  margin: 0 0 8px;
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CategoryDescription = styled.p`
margin: 0;
font - size: 0.875rem;
color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const ActivityForm = styled.div`
margin - bottom: 24px;
padding: 16px;
background - color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
border - radius: 8px;
box - shadow: ${({ theme }) => theme.shadows.sm};
`;

const FormRow = styled.div`
display: flex;
gap: 16px;
margin - bottom: 16px;

@media(max - width: 768px) {
  flex - direction: column;
}
`;

const FormGroup = styled.div`
  flex: 1;
`;

const Label = styled.label`
display: block;
margin - bottom: 8px;
font - weight: 500;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};
  min-height: 80px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const AddButton = styled.button`
  padding: 8px 16px;
  background-color: ${({ theme }) => theme.colors.primary.main};
  color: ${({ theme }) => theme.colors.primary.contrastText};
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.dark};
  }

  &:disabled {
    background-color: ${({ theme }) => theme.colors.action.disabled};
    color: ${({ theme }) => theme.colors.text.disabled};
    cursor: not-allowed;
  }
`;

const PracticesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const EmptyState = styled.div`
  padding: 24px;
  text-align: center;
  color: ${({ theme }) => theme.colors.text.secondary};
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  border: 1px dashed ${({ theme }) => theme.colors.border.main};
`;

const PracticeItem = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border-left: 3px solid ${({ theme }) => theme.colors.primary.main};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
`;

const PracticeHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const PracticeName = styled.h4`
  margin: 0;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const PracticeCategory = styled.span`
font - weight: 500;
color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.primary || '#1976d2'};
background - color: ${({ theme }) =>
    theme.mode === 'dark'
      ? `${theme.colors?.background || '#ffffff'}CC` // Use dark background with opacity in dark mode
      : theme.colors?.background || '#ffffff'};
padding: 2px 8px;
border - radius: 12px;
font - size: 0.75rem;
`;

const RemoveButton = styled.button`
background: none;
border: none;
color: ${({ theme }) => theme.colors?.error || '#f44336'};
cursor: pointer;
font - size: 1rem;
padding: 4px;

  &:hover {
  color: ${({ theme }) => theme.colors?.error || '#f44336'.dark};
}
`;

const PracticeDetails = styled.div`
display: flex;
flex - wrap: wrap;
gap: 16px;
`;

const PracticeImpact = styled.span`
font - size: 0.875rem;
color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const PracticeConsistency = styled.span`
font - size: 0.875rem;
color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const PracticeNotes = styled.p`
margin: 8px 0 0;
font - size: 0.875rem;
color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
width: 100 %;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
position: fixed;
bottom: 24px;
right: 24px;
padding: 8px 16px;
background - color: ${({ theme }) => theme.colors?.success || '#4caf50'};
color: white;
border - radius: 4px;
box - shadow: ${({ theme }) => theme.shadows.md};
opacity: ${(props) => (props.visible ? 1 : 0)};
transform: translateY(${(props) => (props.visible ? 0 : '20px')});
transition:
    opacity 0.3s ease,
  transform 0.3s ease;
pointer - events: none;
`;

// Method selection styles
const MethodSelection = styled.div`
display: grid;
grid - template - columns: 1fr 1fr;
gap: 20px;
margin: 24px 0;
`;

const MethodOption = styled.div`
padding: 20px;
border: 2px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
border - radius: 8px;
cursor: pointer;
transition: all 0.3s ease;

  &:hover {
  border - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  background - color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
}
`;

const MethodTitle = styled.h3`
color: ${({ theme }) => theme.colors?.text || '#000000'};
margin - bottom: 8px;
font - size: 1.1rem;
`;

const MethodDescription = styled.p`
color: ${({ theme }) => theme.colors?.text || '#000000'};
font - size: 0.9rem;
line - height: 1.4;
margin - bottom: 8px;
`;

const MethodBadge = styled.span`
display: inline - block;
padding: 4px 8px;
background - color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
color: white;
border - radius: 12px;
font - size: 0.75rem;
font - weight: 600;
`;
