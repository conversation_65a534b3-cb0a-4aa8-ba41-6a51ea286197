/**
 * Joy Stage Component
 *
 * This component represents the first stage of the Summer season,
 * focusing on finding deeper joy through meaningful activities and relationships.
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from '../../../../theme/SimpleThemeProvider';
import { useSeasonsOfSelf } from '../../context/SeasonsOfSelfContext';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import autoSaveService from '../../../DataPortability/services/autoSaveService';
import { StrengthsAssessment } from './StrengthsAssessment';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// Component props
interface JoyStageProps {
  onComplete?: () => void;
}

// Joy source type
interface JoySource {
  id: string;
  source: string;
  meaningLevel: number;
  frequency: string;
  impact: string;
  notes: string;
}

/**
 * Joy Stage Component
 */
export const JoyStage: React.FC<JoyStageProps> = ({ onComplete }) => {
  // Theme is used in styled components via ThemeProvider
  useTheme();
  const { data, updateData, stages, updateStageCompletion } = useSeasonsOfSelf();

  // State for choosing assessment method
  const [assessmentMethod, setAssessmentMethod] = useState<'choose' | 'traditional' | 'strengths'>(
    'choose'
  );

  // Get saved data or initialize with defaults
  const [joySources, setJoySources] = useState<JoySource[]>(
    data.summer && data.summer.joySources ? data.summer.joySources : []
  );

  const [newJoySource, setNewJoySource] = useState<JoySource>({
    id: '',
    source: '',
    meaningLevel: 0,
    frequency: '',
    impact: '',
    notes: '',
  });

  // Save indicator state
  const [showSaveIndicator, setShowSaveIndicator] = useState(false);

  // Create auto-save instance
  const autoSave = React.useMemo(
    () =>
      autoSaveService.createEnhancedAutoSave({
        key: 'summer_joy_sources',
        onSave: () => {
          setShowSaveIndicator(true);
          setTimeout(() => setShowSaveIndicator(false), 2000);
        },
      }),
    []
  );

  // Update context data when joy sources change
  useEffect(() => {
    updateData('summer', 'joySources', joySources);

    // Auto-save data
    autoSave.save(joySources);

    // Mark stage as completed if at least 3 joy sources are added
    const joyStage = stages.find((stage) => stage.id === 'joy');
    if (joyStage && !joyStage.completed && joySources.length >= 3) {
      updateStageCompletion('joy', true);
    } else if (joyStage && joyStage.completed && joySources.length < 3) {
      updateStageCompletion('joy', false);
    }
  }, [joySources, updateData, stages, autoSave, updateStageCompletion]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setNewJoySource((prev) => ({
      ...prev,
      [name]: name === 'meaningLevel' ? parseInt(value) || 0 : value,
    }));
  };

  // Handle adding a new joy source
  const handleAddJoySource = () => {
    if (!newJoySource.source) return;

    const newSource = {
      ...newJoySource,
      id: `joy-${Date.now()}`,
    };

    setJoySources((prev) => [...prev, newSource]);
    setNewJoySource({
      id: '',
      source: '',
      meaningLevel: 0,
      frequency: '',
      impact: '',
      notes: '',
    });
  };

  // Handle removing a joy source
  const handleRemoveJoySource = (id: string) => {
    setJoySources((prev) => prev.filter((source) => source.id !== id));
  };

  // Handle completion
  const handleComplete = () => {
    if (onComplete) {
      onComplete();
    }
  };

  // Check if user has existing data to determine initial method
  useEffect(() => {
    if (data.summer?.strengthsData) {
      setAssessmentMethod('strengths');
    } else if (data.summer?.joySources && data.summer.joySources.length > 0) {
      setAssessmentMethod('traditional');
    }
  }, [data.summer]);

  // Render method selection
  if (assessmentMethod === 'choose') {
    return (
      <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
        <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
          <CardHeader>
            <StageTitle>Joy Stage</StageTitle>
            <StageDescription>
              Choose your preferred method to discover deeper joy and fulfillment in your life.
            </StageDescription>
          </CardHeader>

          <CardContent>
            <MethodSelection>
              <MethodOption onClick={() => setAssessmentMethod('strengths')}>
                <MethodTitle>⭐ Character Strengths Assessment</MethodTitle>
                <MethodDescription>
                  Discover your signature character strengths and learn how to apply them for
                  greater joy and fulfillment.
                </MethodDescription>
                <MethodBadge>Recommended</MethodBadge>
              </MethodOption>

              <MethodOption onClick={() => setAssessmentMethod('traditional')}>
                <MethodTitle>😊 Joy Sources Exploration</MethodTitle>
                <MethodDescription>
                  Identify and explore the activities, relationships, and experiences that bring you
                  lasting joy.
                </MethodDescription>
              </MethodOption>
            </MethodSelection>
          </CardContent>
        </StageCard>
      </Container>
    );
  }

  // Render Character Strengths Assessment
  if (assessmentMethod === 'strengths') {
    return (
      <StrengthsAssessment
        onComplete={onComplete}
        onBack={() => setAssessmentMethod('choose')}
        initialData={data.summer?.strengthsData}
      />
    );
  }

  // Render traditional joy sources exploration
  return (
    <Container as={motion.div} variants={containerVariants} initial="hidden" animate="visible">
      <StageCard elevation="medium" as={motion.div} variants={itemVariants}>
        <CardHeader>
          <StageTitle>Joy Stage</StageTitle>
          <StageDescription>
            The Joy Stage is about finding deeper joy through meaningful activities and
            relationships. Identify sources of joy that provide a sense of meaning and purpose.
          </StageDescription>
        </CardHeader>

        <CardContent>
          <Section>
            <SectionTitle>Your Sources of Joy</SectionTitle>
            <SectionDescription>
              List activities, relationships, or practices that bring you a deeper sense of joy and
              meaning. These could be creative pursuits, meaningful relationships, or activities
              that align with your values.
            </SectionDescription>

            <SourceForm>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="source">Source of Joy</Label>
                  <Input
                    id="source"
                    name="source"
                    value={newJoySource.source}
                    onChange={handleInputChange}
                    placeholder="e.g., Volunteering at the animal shelter"
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="meaningLevel">Meaning Level (1-10)</Label>
                  <Input
                    id="meaningLevel"
                    name="meaningLevel"
                    type="number"
                    min="1"
                    max="10"
                    value={newJoySource.meaningLevel || ''}
                    onChange={handleInputChange}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="frequency">Frequency</Label>
                  <Select
                    id="frequency"
                    name="frequency"
                    value={newJoySource.frequency}
                    onChange={handleInputChange}
                  >
                    <option value="">Select frequency</option>
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                    <option value="occasionally">Occasionally</option>
                    <option value="rarely">Rarely</option>
                  </Select>
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="impact">Impact on Life</Label>
                  <Select
                    id="impact"
                    name="impact"
                    value={newJoySource.impact}
                    onChange={handleInputChange}
                  >
                    <option value="">Select impact</option>
                    <option value="transformative">Transformative</option>
                    <option value="significant">Significant</option>
                    <option value="moderate">Moderate</option>
                    <option value="minimal">Minimal</option>
                  </Select>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup fullWidth>
                  <Label htmlFor="notes">Notes</Label>
                  <TextArea
                    id="notes"
                    name="notes"
                    value={newJoySource.notes}
                    onChange={handleInputChange}
                    placeholder="Why does this bring you joy? How does it connect to your values?"
                    rows={3}
                  />
                </FormGroup>
              </FormRow>

              <AddButton onClick={handleAddJoySource} disabled={!newJoySource.source}>
                Add Source of Joy
              </AddButton>
            </SourceForm>

            <SourcesList>
              {joySources.length === 0 ? (
                <EmptyState>No sources of joy added yet. Add your first source above.</EmptyState>
              ) : (
                joySources.map((source) => (
                  <SourceItem key={source.id}>
                    <SourceHeader>
                      <SourceName>{source.source}</SourceName>
                      <MeaningLevel>Meaning: {source.meaningLevel}/10</MeaningLevel>
                      <RemoveButton onClick={() => handleRemoveJoySource(source.id)}>
                        ✕
                      </RemoveButton>
                    </SourceHeader>
                    <SourceDetails>
                      <SourceFrequency>Frequency: {source.frequency}</SourceFrequency>
                      <SourceImpact>Impact: {source.impact}</SourceImpact>
                      {source.notes && <SourceNotes>{source.notes}</SourceNotes>}
                    </SourceDetails>
                  </SourceItem>
                ))
              )}
            </SourcesList>
          </Section>

          <SaveIndicator visible={showSaveIndicator}>Changes saved automatically</SaveIndicator>
        </CardContent>

        <CardActions>
          <Button variant="primary" onClick={handleComplete} disabled={joySources.length < 3}>
            {joySources.length < 3
              ? `Add ${3 - joySources.length} more ${joySources.length === 2 ? 'source' : 'sources'} to continue`
              : 'Continue to Momentum Stage'}
          </Button>
        </CardActions>
      </StageCard>
    </Container>
  );
};

// Styled components
const Container = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const StageCard = styled(Card)`
  overflow: hidden;
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const CardHeader = styled.div`
  padding: 24px;
  background-color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.background || '#ffffff' // Use dark background in dark mode
      : theme.colors?.background || '#ffffff'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const StageTitle = styled.h2`
  color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  margin-bottom: 8px;
`;

const StageDescription = styled.p`
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const CardContent = styled.div`
  padding: 24px;
`;

const CardActions = styled.div`
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
`;

const Section = styled.div`
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  margin-bottom: 8px;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SectionDescription = styled.p`
  margin-bottom: 16px;
  color: ${({ theme }) =>
    theme.mode === 'dark'
      ? theme.colors?.text || '#000000' // Use primary text color in dark mode for better contrast
      : theme.colors?.text || '#000000'};
`;

const SourceForm = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const FormRow = styled.div`
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  @media (max-width: 600px) {
    flex-direction: column;
  }
`;

const FormGroup = styled.div<{ fullWidth?: boolean }>`
  flex: ${(props) => (props.fullWidth ? 1 : 0.5)};

  @media (max-width: 600px) {
    flex: 1;
  }
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.primary};
`;

const Input = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid ${({ theme }) => theme.colors.border.main};
  border-radius: 4px;
  font-size: 1rem;
  background-color: ${({ theme }) => theme.colors.background.input};
  color: ${({ theme }) => theme.colors.text.primary};
  resize: vertical;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors.primary.main};
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.primary.light};
  }
`;

const AddButton = styled(Button)`
  margin-top: 8px;
`;

const SourcesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

const SourceItem = styled.div`
  padding: 16px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 8px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  border-left: 4px solid ${({ theme }) => theme.colors?.primary || '#1976d2'};
`;

const SourceHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const SourceName = styled.h4`
  margin: 0;
  flex: 1;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const MeaningLevel = styled.span`
  margin-right: 16px;
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border-radius: 4px;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors.primary.main};
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors.text.secondary};
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;

  &:hover {
    color: ${({ theme }) => theme.colors.error.main};
  }
`;

const SourceDetails = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
`;

const SourceFrequency = styled.span`
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 4px;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SourceImpact = styled.span`
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 4px;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
`;

const SourceNotes = styled.p`
  margin: 8px 0 0;
  font-size: 0.875rem;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  width: 100%;
`;

const EmptyState = styled.div`
  padding: 24px;
  text-align: center;
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  background-color: ${({ theme }) => theme.colors?.paper || '#ffffff'};
  border-radius: 8px;
`;

const SaveIndicator = styled.div<{ visible: boolean }>`
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transition: opacity 0.3s ease;
  text-align: center;
  color: ${({ theme }) => theme.colors?.success || '#4caf50'};
  margin-top: 16px;
`;

// Method selection styles
const MethodSelection = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 24px 0;
`;

const MethodOption = styled.div`
  padding: 20px;
  border: 2px solid ${({ theme }) => theme.colors?.border || '#e0e0e0'};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
    background-color: ${({ theme }) => `${theme.colors?.primary || '#1976d2'}10`};
  }
`;

const MethodTitle = styled.h3`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  margin-bottom: 8px;
  font-size: 1.1rem;
`;

const MethodDescription = styled.p`
  color: ${({ theme }) => theme.colors?.text || '#000000'};
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 8px;
`;

const MethodBadge = styled.span`
  display: inline-block;
  padding: 4px 8px;
  background-color: ${({ theme }) => theme.colors?.primary || '#1976d2'};
  color: white;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
`;
