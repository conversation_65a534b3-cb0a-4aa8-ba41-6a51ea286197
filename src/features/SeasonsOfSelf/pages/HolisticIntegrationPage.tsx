/**
 * Holistic Integration Page
 *
 * This page provides a comprehensive integration dashboard that combines
 * Life Profile Dashboard, Integrated Insights, and Personalized Recommendations.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../theme/SimpleThemeProvider';
import LifeProfileDashboard from '../components/integration/LifeProfileDashboard';
import IntegratedInsights from '../components/integration/IntegratedInsights';
import PersonalizedRecommendations from '../components/integration/PersonalizedRecommendations';

type ActiveView = 'dashboard' | 'insights' | 'recommendations';

const HolisticIntegrationPage: React.FC = () => {
  const { theme } = useTheme();
  const [activeView, setActiveView] = useState<ActiveView>('dashboard');

  const handleViewInsights = () => {
    setActiveView('insights');
  };

  const handleViewRecommendations = () => {
    setActiveView('recommendations');
  };

  const handleTakeAction = (actionId: string) => {
    // Handle action based on actionId
    console.log('Taking action:', actionId);

    // Navigate to appropriate section based on action
    if (actionId.includes('personal-info') || actionId.includes('financial')) {
      // Navigate to Financial Compass
      window.location.href = '/financial-compass';
    } else if (actionId.includes('spring') || actionId.includes('life')) {
      // Navigate to Life Journey
      window.location.href = '/seasons';
    }
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'insights':
        return <IntegratedInsights onViewRecommendations={handleViewRecommendations} />;
      case 'recommendations':
        return <PersonalizedRecommendations onTakeAction={handleTakeAction} />;
      default:
        return (
          <LifeProfileDashboard
            onViewInsights={handleViewInsights}
            onViewRecommendations={handleViewRecommendations}
          />
        );
    }
  };

  return (
    <Container theme={theme}>
      {/* Navigation Header */}
      <NavigationHeader theme={theme}>
        <NavigationTitle theme={theme}>Holistic Integration Dashboard</NavigationTitle>
        <NavigationTabs>
          <NavigationTab
            active={activeView === 'dashboard'}
            onClick={() => setActiveView('dashboard')}
            theme={theme}
          >
            <TabIcon>📊</TabIcon>
            <TabLabel>Life Profile</TabLabel>
          </NavigationTab>
          <NavigationTab
            active={activeView === 'insights'}
            onClick={() => setActiveView('insights')}
            theme={theme}
          >
            <TabIcon>🔍</TabIcon>
            <TabLabel>Insights</TabLabel>
          </NavigationTab>
          <NavigationTab
            active={activeView === 'recommendations'}
            onClick={() => setActiveView('recommendations')}
            theme={theme}
          >
            <TabIcon>🎯</TabIcon>
            <TabLabel>Recommendations</TabLabel>
          </NavigationTab>
        </NavigationTabs>
      </NavigationHeader>

      {/* Content Area */}
      <ContentArea>{renderActiveView()}</ContentArea>

      {/* Quick Actions Footer */}
      <QuickActionsFooter theme={theme}>
        <QuickActionButton
          onClick={() => (window.location.href = '/financial-compass')}
          theme={theme}
        >
          <ActionIcon>💰</ActionIcon>
          <ActionLabel>Financial Compass</ActionLabel>
        </QuickActionButton>
        <QuickActionButton onClick={() => (window.location.href = '/seasons')} theme={theme}>
          <ActionIcon>🌱</ActionIcon>
          <ActionLabel>Life Journey</ActionLabel>
        </QuickActionButton>
        <QuickActionButton
          onClick={() => (window.location.href = '/data-portability')}
          theme={theme}
        >
          <ActionIcon>📤</ActionIcon>
          <ActionLabel>Export Data</ActionLabel>
        </QuickActionButton>
      </QuickActionsFooter>
    </Container>
  );
};

// Styled Components
const Container = styled.div`
  background-color: ${({ theme }) => theme.colors.background.default};
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const NavigationHeader = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-bottom: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: 16px 24px;
  box-shadow: ${({ theme }) => theme.shadows.sm};
  position: sticky;
  top: 0;
  z-index: 100;
`;

const NavigationTitle = styled.h1`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 16px 0;
  text-align: center;

  @media (max-width: 768px) {
    font-size: 1.25rem;
    margin-bottom: 12px;
  }
`;

const NavigationTabs = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;

  @media (max-width: 768px) {
    gap: 4px;
  }
`;

const NavigationTab = styled.button<{ active: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 20px;
  border: 2px solid
    ${({ active, theme }) => (active ? theme.colors.primary.main : theme.colors.border.main)};
  background-color: ${({ active, theme }) =>
    active ? theme.colors.primary.main : theme.colors.background.paper};
  color: ${({ active, theme }) =>
    active ? theme.colors.primary.contrastText : theme.colors.text.primary};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 100px;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary.main};
    background-color: ${({ active, theme }) =>
      active ? theme.colors.primary.dark : theme.colors.primary.light};
  }

  @media (max-width: 768px) {
    padding: 8px 12px;
    min-width: 80px;
  }
`;

const TabIcon = styled.span`
  font-size: 1.25rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const TabLabel = styled.span`
  font-size: 0.875rem;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }
`;

const ContentArea = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const QuickActionsFooter = styled.div`
  background-color: ${({ theme }) => theme.colors.background.paper};
  border-top: 1px solid ${({ theme }) => theme.colors.border.light};
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  gap: 16px;
  box-shadow: ${({ theme }) => theme.shadows.sm};

  @media (max-width: 768px) {
    padding: 12px 16px;
    gap: 12px;
  }
`;

const QuickActionButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background-color: ${({ theme }) => theme.colors.background.secondary};
  border: 1px solid ${({ theme }) => theme.colors.border.light};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;

  &:hover {
    background-color: ${({ theme }) => theme.colors.primary.light};
    border-color: ${({ theme }) => theme.colors.primary.main};
  }

  @media (max-width: 768px) {
    padding: 8px 12px;
    min-width: 60px;
  }
`;

const ActionIcon = styled.span`
  font-size: 1.25rem;

  @media (max-width: 768px) {
    font-size: 1rem;
  }
`;

const ActionLabel = styled.span`
  font-size: 0.75rem;
  font-weight: 500;
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;

  @media (max-width: 768px) {
    font-size: 0.625rem;
  }
`;

export default HolisticIntegrationPage;
